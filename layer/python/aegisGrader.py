import json
import time
import sys
import traceback
from contextlib import ExitStack
from tempfile import NamedTemporaryFile
from processOCR import process_all_pages, combine_and_evaluate, create_rubric, extract_content_from_docs
# from model import batchProcessing


PDF_BASE64_PREFIX = "data:application/pdf;base64,"

# Removed _TemporaryFileWrapper import - no longer needed with optimized workflow

# Utility to save PDF bytes to a temp file
def save_pdf_bytes_to_tempfile(pdf_bytes, suffix):
    temp_pdf_file = NamedTemporaryFile(mode="wb", suffix=f"_{suffix}.pdf", delete=False)
    temp_pdf_file.write(pdf_bytes)
    temp_pdf_file.flush()
    return temp_pdf_file

# Removed process_pdf_to_jsonl function - now using direct document processing for optimization


def doRubric(rubricPdfBytes, questionPaperPdfBytes=None, subject=None, save_md_files=False):
    """
    Process the rubric PDF bytes and return rubric content directly.
    Optimized to return content instead of file paths.
    """
    if not rubricPdfBytes:
        if (not questionPaperPdfBytes):
            raise ValueError("No rubric PDF bytes provided.")
        print("No rubric provided. Generating rubric from question paper...", file=sys.stderr)
        temp_qp_pdf = save_pdf_bytes_to_tempfile(questionPaperPdfBytes, "question_paper")
        # Process question paper to get document objects directly
        question_paper_docs = process_all_pages(temp_qp_pdf.name, "question_paper", subject)
        # Generate rubric content directly from documents
        rubric_content = create_rubric(question_paper_docs, save_md_files)
        return rubric_content
    temp_rubric_pdf = save_pdf_bytes_to_tempfile(rubricPdfBytes, "rubric")
    # Process rubric to get document objects directly
    rubric_docs = process_all_pages(temp_rubric_pdf.name, "rubric", subject)
    # Extract content directly from documents
    rubric_content = extract_content_from_docs(rubric_docs)
    return rubric_content

def doParallelAnswerSheet(answerSheetBytesKeyTuple, rubricContentAndSubject, child_conn):
    """
    Process the answer sheet PDF bytes against the rubric content.
    Optimized to work with direct content instead of file paths.
    Returns evaluation content as a string.
    """
    answerSheetKey = ""
    try:
        rubric_content = rubricContentAndSubject[0]
        subject = rubricContentAndSubject[1]
        answerSheetBytes = answerSheetBytesKeyTuple[0]
        answerSheetKey = answerSheetBytesKeyTuple[1]
        # print(f"got answerSheetKey: {answerSheetKey}, got answer sheet bytes type: {answerSheetBytes}")
        if not answerSheetBytes:
            raise ValueError("No answer sheet PDF bytes provided.")

        temp_answer_sheet_pdf = save_pdf_bytes_to_tempfile(answerSheetBytes, "answers")
        # Process answer sheet to get document objects directly
        answer_sheet_docs = process_all_pages(temp_answer_sheet_pdf.name, "answer_sheet", subject)

        print(f"Evaluating answer sheet against rubric...", file=sys.stderr)
        # Pass document objects and content directly
        evaluation_result_doc = combine_and_evaluate(answer_sheet_docs, rubric_content, subject)

        if not hasattr(evaluation_result_doc, 'page_content'):
            raise ValueError("Evaluation result lacks 'page_content'. Cannot proceed.")

        child_conn.send((evaluation_result_doc.page_content, answerSheetKey)) # Return the evaluation content as a string
    except Exception as e:
        print(f"Error processing answer sheet, e: {e}")
        child_conn.send(("Error: processing answer sheet, please try again", answerSheetKey))

# New entry point for Lambda: accepts rubric PDF bytes and a list of answer sheet PDF bytes

def run_grader_with_pdf_bytes(rubric_pdf_bytes, answer_sheet_pdf_bytes_list, save_md_files=False, question_paper_pdf_bytes=None, subject=None):
    """
    Main grading entry point for Lambda or programmatic use.
    If rubric_pdf_bytes is None, will attempt to generate rubric using question_paper_pdf_bytes.
    """
    all_evaluation_contents = []
    # created_md_files = []
    with ExitStack() as stack:
        try:
            # If rubric is missing, generate it from question paper
            if rubric_pdf_bytes is None:
                if question_paper_pdf_bytes is None:
                    raise ValueError("No rubric or question paper PDF bytes provided.")
                print("No rubric provided. Generating rubric from question paper...", file=sys.stderr)
                temp_qp_pdf = stack.enter_context(save_pdf_bytes_to_tempfile(question_paper_pdf_bytes, "question_paper"))
                # Process question paper to get document objects directly
                question_paper_docs = process_all_pages(temp_qp_pdf.name, "question_paper", subject)
                # Generate rubric content directly from documents
                rubric_content = create_rubric(question_paper_docs, save_md_files)
                print(f"Generated rubric from question paper documents", file=sys.stderr)
            else:
                temp_rubric_pdf = stack.enter_context(save_pdf_bytes_to_tempfile(rubric_pdf_bytes, "rubric"))
                # Process rubric to get document objects directly
                rubric_docs = process_all_pages(temp_rubric_pdf.name, "rubric", subject)
                # Extract content directly from documents
                rubric_content = extract_content_from_docs(rubric_docs)
                print(f"Processed rubric from PDF documents", file=sys.stderr)
            print(f"\nFound {len(answer_sheet_pdf_bytes_list)} answer sheets to process.", file=sys.stderr)
            # inlineRequestList = []
            for i, ans_bytes in enumerate(answer_sheet_pdf_bytes_list):
                sheet_index = i + 1
                print(f"--- Processing Answer Sheet {sheet_index} ---", file=sys.stderr)
                try:
                    temp_answer_sheet_pdf = stack.enter_context(save_pdf_bytes_to_tempfile(ans_bytes, f"answers_{sheet_index}"))
                    # Process answer sheet to get document objects directly
                    answer_sheet_docs = process_all_pages(temp_answer_sheet_pdf.name, "answer_sheet", subject)
                    print(f"Evaluating answer sheet {sheet_index} against rubric...", file=sys.stderr)
                    # Pass document objects directly instead of file paths
                    evaluation_result_doc = combine_and_evaluate(answer_sheet_docs, rubric_content, subject, save_md_files=save_md_files)
                    # inlineRequestList.append(combine_and_evaluate(answer_sheet_docs, rubric_content, subject, batch=True))
                    if not hasattr(evaluation_result_doc, 'page_content'):
                        print(f"Error: Evaluation result for sheet {sheet_index} lacks 'page_content'. Skipping.", file=sys.stderr)
                        continue
                    evaluation_content = evaluation_result_doc.page_content
                    all_evaluation_contents.append(evaluation_content)
                    # result_filename = f"/tmp/evaluation_result_sheet_{sheet_index}.md"
                    # created_md_files.append(result_filename)
                    # if save_md_files:
                    #     try:
                    #         with open(result_filename, "w") as md_file:
                    #             md_file.write(evaluation_content)
                    #         print(f"Detailed evaluation for sheet {sheet_index} saved to {result_filename}", file=sys.stderr)
                    #     except IOError as e:
                    #         print(f"Warning: Could not save detailed evaluation file {result_filename}: {e}", file=sys.stderr)
                    print(f"--- Finished Answer Sheet {sheet_index} ---", file=sys.stderr)
                except (ValueError, TypeError, RuntimeError, FileNotFoundError, json.JSONDecodeError) as e:
                    print(f"\n!! Error processing Answer Sheet {sheet_index}: {type(e).__name__} - {str(e)} !!", file=sys.stderr)
                    traceback.print_exc(file=sys.stderr)
                    print(f"Skipping evaluation for sheet {sheet_index} due to error.", file=sys.stderr)
                except Exception as e:
                    print(f"\n!! Unexpected System Error during Answer Sheet {sheet_index}: {type(e).__name__} - {str(e)} !!", file=sys.stderr)
                    traceback.print_exc(file=sys.stderr)
                    print(f"Skipping evaluation for sheet {sheet_index} due to unexpected error.", file=sys.stderr)
            # if len(inlineRequestList) > 0:
            #     all_evaluation_contents, created_md_files = batchProcessing(inlineRequestList, prompt=evaluation_prompt)
            #     # proceed with the batch job
            print(f"\nCombining results from {len(all_evaluation_contents)} successfully evaluated sheets.", file=sys.stderr)
            final_output_string = "\n\n".join(all_evaluation_contents)
            return final_output_string
        
        except (ValueError, TypeError, RuntimeError, FileNotFoundError, json.JSONDecodeError) as e:
            print(f"\n!! Critical Processing Error: {type(e).__name__} - {str(e)} !!", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            return None
        
        except Exception as e:
            print(f"\n!! Critical Unexpected System Error: {type(e).__name__} - {str(e)} !!", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            return None
        
        finally:
            print("\nTemporary file cleanup handled by ExitStack.", file=sys.stderr)
            # if created_md_files and save_md_files:
            #     print(f"Note: Detailed evaluation markdown files created: {', '.join(created_md_files)}", file=sys.stderr)

# Keep the old CLI entry point for local testing if needed
if __name__ == "__main__":
    print("This script is now intended to be used as a module for Lambda integration.", file=sys.stderr)
