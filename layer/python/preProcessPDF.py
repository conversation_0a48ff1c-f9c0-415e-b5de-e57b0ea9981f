from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>, PdfWriter
import io
import os
import sys

def get_pdf_reader_and_metadata(pdf_path):
    reader = PdfReader(pdf_path)
    metadata = {
        "total_pages": len(reader.pages),
        "title": reader.metadata.get('/Title', ''),
        "author": reader.metadata.get('/Author', ''),
        "creation_date": reader.metadata.get('/CreationDate', '')
    }
    return reader, metadata

def extract_page(reader, page_number):
    """
    Extracts a single page from a PDF reader and returns it as bytes.
    """
    total_pages = len(reader.pages)
    if page_number < 0 or page_number >= total_pages:
        print(f"Page {page_number} is out of range. This PDF has {total_pages} pages.", file=sys.stderr)
        return None

    writer = PdfWriter()
    writer.add_page(reader.pages[page_number])

    buffer = io.BytesIO()
    writer.write(buffer)

    return buffer.getvalue()