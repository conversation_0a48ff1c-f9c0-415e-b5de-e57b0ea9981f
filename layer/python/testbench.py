import re
from langchain_core.documents import Document

def reorder_pages(pages, generic_metadata):
    '''
    Return type should be list of {page_content: "content", metadata: {generic + page number}}
    '''
    pattern = r'page_number:\s*\"?(\d+)\"?'

    documentList = []

    for page in pages:
        # find the page number in the content
        match = re.search(pattern, page)
        metadata = generic_metadata.copy()
        if match:
            page_number = int(match.group(1))
            metadata['page'] = page_number
            documentList.append(Document(page_content= page, metadata=metadata))
        else:
            print(f'[Mehul] page number not found in the content')

    # sort the documents by metadata page number
    documentList.sort(key=lambda doc: doc.metadata.get('page', 0))
    return documentList

def checkLimit():
    from google import genai
    client = genai.Client(api_key="AIzaSyD6gzhlbkio90SFhjYy0BOFl36JIHYauF0")

    for job in client.list_batch_jobs():
        print(f"Job Name: {job.name}, State: {job.state.name}, Display Name: {job.config.display_name}")

if __name__ == "__main__":
    # pages = [
    #     """<metadata>
    # Total Pages: 1
    # Subject: Physics
    # Date: 2024-01-10
    # page_number: "2"
    # </metadata>
    #
    # <page number="1">
    # <question number="1">
    # The formula for kinetic energy is $KE = \frac{1}{2}mv^2$.
    # Given $m=5$ kg and $v=10$ m/s,
    # $KE = \frac{1}{2} \times 5 \times (10)^2$
    # $KE = \frac{1}{2} \times 5 \times 100$
    # $KE = 250$ Joules.
    # </question>
    #
    # <question number="2">
    # Newton's second law states $F = ma$.
    # </question>
    # </page>""",
    #     
    #
    #    """<metadata>
    # Total Pages: 1
    # Subject: Physics
    # Date: 2024-01-10
    # page_number: 1
    # </metadata>
    #
    # <page number="1">
    # <question number="1">
    # The formula for kinetic energy is $KE = \frac{1}{2}mv^2$.
    # Given $m=5$ kg and $v=10$ m/s,
    # $KE = \frac{1}{2} \times 5 \times (10)^2$
    # $KE = \frac{1}{2} \times 5 \times 100$
    # $KE = 250$ Joules.
    # </question>
    #
    # <question number="2">
    # Newton's second law states $F = ma$.
    # </question>
    # </page>"""
    # ]
    #
    # documentList = reorder_pages(pages, {"total_pages" : 2, "subject": "Physics", "date": "2024-01-10"})
    # for doc in documentList:
    #     print(f"got page number: {doc.metadata['page']}")
    checkLimit()

