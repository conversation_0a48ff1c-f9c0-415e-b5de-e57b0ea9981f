from contextlib import ExitStack
import json
from concurrent.futures import Thread<PERSON>oolExecutor
from functools import partial
import boto3
import sys
import os
from datetime import datetime
import pymongo
from bson import ObjectId

# For local testing, add layer path
if os.environ.get('AWS_SAM_LOCAL'):
    sys.path.append('/opt/python')
    sys.path.append('/opt/python/python')
else:
    sys.path.append('/opt/python')
    sys.path.append('/opt/python/python')

from aegisGrader import run_grader_with_pdf_bytes, doRubric, doParallelAnswerSheet
from parallels import Manager

# Use default credential chain (AWS CLI for local, IAM role for Lambda)
s3_client = boto3.client('s3')
                        

# MongoDB setup
def get_mongo_client():
    # Use test DB if running locally, prod DB otherwise
    is_local = os.environ.get('AWS_SAM_LOCAL')
    env_var = 'MONGO_URI_TEST' if is_local else 'MONGO_URI_PROD'
    mongo_uri = os.environ.get(env_var)
    
    print(f"Environment: {'LOCAL' if is_local else 'PROD'}")
    print(f"Using environment variable: {env_var}")
    print(f"Connection string exists: {bool(mongo_uri)}")
    
    if not mongo_uri:
        raise ValueError(f"{env_var} environment variable not set")
    
    # Log connection string (masked for security)
    masked_uri = mongo_uri.replace(mongo_uri.split('@')[0].split('//')[1], '***:***') if '@' in mongo_uri else mongo_uri
    print(f"Connecting to: {masked_uri}")
    
    try:
        client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        # Test the connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        return client
    except Exception as e:
        print(f"❌ MongoDB connection failed: {str(e)}")
        raise

def create_grading_document(manifest_data):
    """Create the initial grading document with empty answer sheets array"""
    try:
        print("Creating initial grading document...")
        client = get_mongo_client()

        is_local = os.environ.get('AWS_SAM_LOCAL')
        db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'
        print(f"Using database: {db_name}")

        db = client[db_name]
        collection = db['AegisGrader']

        # Extract files by purpose
        question_paper = None
        rubric = None

        for file in manifest_data.get('files', []):
            if file.get('filePurpose') == 'question_paper':
                question_paper = file
            elif file.get('filePurpose') == 'rubric':
                rubric = file

        # Create the main document with empty answer sheets array
        aegis_grader_doc = {
            "testDetails": {
                "createdBy": manifest_data['testDetails'].get('CreatedBy', ''),
                "className": manifest_data['testDetails'].get('className', ''),
                "subject": manifest_data['testDetails'].get('subject', ''),
                "date": manifest_data['testDetails'].get('date', datetime.now().isoformat())
            },
            "answerSheets": [],  # Empty array to start with
            "questionPaper": {
                "type": "questionPaper",
                "pdfUrl": question_paper.get('key', '') if question_paper else '',
                "timestamp": question_paper.get('timestamp') if question_paper else int(datetime.now().timestamp() * 1000)
            },
            "rubric": {
                "type": "rubric",
                "pdfUrl": rubric.get('key', '') if rubric else '',
                "timestamp": rubric.get('timestamp') if rubric else int(datetime.now().timestamp() * 1000)
            },
            "createdAt": datetime.now(),
            "updatedAt": datetime.now()
        }

        result = collection.insert_one(aegis_grader_doc)
        document_id = result.inserted_id
        print(f"Created grading document with ID: {document_id}")

        client.close()
        return document_id

    except Exception as e:
        print(f"Error creating grading document: {str(e)}")
        return None

def append_answer_sheet_to_document(document_id, manifest_data, evaluation_result):
    """Append a single answer sheet evaluation to the existing document"""
    try:
        if not evaluation_result or not document_id:
            return False

        print(f"Appending answer sheet to document {document_id}...")
        client = get_mongo_client()

        is_local = os.environ.get('AWS_SAM_LOCAL')
        db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'

        db = client[db_name]
        collection = db['AegisGrader']

        # evaluation_result is a tuple: (evaluation_text, answer_sheet_key)
        evaluation_text, answer_sheet_key = evaluation_result

        # Find the corresponding answer sheet file info
        answer_sheet_file = None
        for file in manifest_data.get('files', []):
            if file.get('filePurpose') == 'answer_sheet' and file.get('key') == answer_sheet_key:
                answer_sheet_file = file
                break

        if not answer_sheet_file:
            print(f"Could not find answer sheet file info for key: {answer_sheet_key}")
            client.close()
            return False

        # Determine if this is an error result
        is_error = evaluation_text.startswith("Error:")

        # Create answer sheet document
        answer_sheet_doc = {
            "id": answer_sheet_file.get('key'),
            "studentName": answer_sheet_file.get('studentName', ''),
            "rollNumber": answer_sheet_file.get('rollNumber', ''),
            "pdfUrl": answer_sheet_file.get('key'),  # Using S3 key as URL reference
            "timestamp": answer_sheet_file.get('timestamp'),
            "className": manifest_data['testDetails'].get('className', ''),
            "evaluationResult": evaluation_text,
            "answerSheetKey": answer_sheet_key,
            "status": "error" if is_error else "completed",
            "processedAt": datetime.now().isoformat()
        }

        # Use $push to append the answer sheet to the existing document
        result = collection.update_one(
            {"_id": ObjectId(document_id)},
            {
                "$push": {"answerSheets": answer_sheet_doc},
                "$set": {"updatedAt": datetime.now()}
            }
        )

        if result.modified_count > 0:
            print(f"Successfully appended answer sheet for student {answer_sheet_file.get('studentName', 'Unknown')}")
            client.close()
            return True
        else:
            print(f"Failed to append answer sheet - document not found or not modified")
            client.close()
            return False

    except Exception as e:
        print(f"Error appending answer sheet to MongoDB: {str(e)}")
        return False

def process_files(data):
    answer_sheet_keys = []
    question_paper_key = None
    rubric_key = None

    for file in data['files']:
        purpose = file.get('filePurpose')
        key = file.get('key')
        if purpose == 'answer_sheet':
            answer_sheet_keys.append(key)
        elif purpose == 'question_paper':
            question_paper_key = key
        elif purpose == 'rubric':
            rubric_key = key

    return {
        "answer_sheet_keys": answer_sheet_keys,
        "question_paper_key": question_paper_key,
        "rubric_key": rubric_key
    }

def download_pdf(bucket, key):
    response = s3_client.get_object(Bucket=bucket, Key=key)
    pdf_bytes = response['Body'].read()
    return pdf_bytes

def lambda_handler(event, context):
    records = event["Records"]
    # print("Event => ", event)

    for record in records:
        body = json.loads(record["body"])
        s3 = body["Records"][0]["s3"]
        bucket = s3["bucket"]["name"]
        key = s3["object"]["key"]

        # Fetch and parse the manifest JSON from S3
        print(f"[Mehul] Debug got key: {key} in bucket: {bucket}")
        response = s3_client.get_object(Bucket=bucket, Key=key)
        # print("Response => ", response)
        content = response['Body'].read().decode('utf-8')
        data = json.loads(content)

        # Get the keys for the PDFs
        result = process_files(data)
        answer_sheet_keys = result["answer_sheet_keys"]
        question_paper_key = result["question_paper_key"]
        rubric_key = result["rubric_key"]

        # Download PDFs and store in variable (ram memory)
        pdfs = {}

        if question_paper_key:
            pdfs['question_paper'] = download_pdf(bucket, question_paper_key)
        if rubric_key:
            pdfs['rubric'] = download_pdf(bucket, rubric_key)
        pdfs['answer_sheets'] = []
        # answerSheetBytesTupleList = []
        for ans_key in answer_sheet_keys:
            pdfs['answer_sheets'].append((download_pdf(bucket, ans_key), ans_key))


        # evaluated_results = run_grader_with_pdf_bytes(
        #     rubric_pdf_bytes=pdfs.get('rubric'),
        #     answer_sheet_pdf_bytes_list=pdfs.get('answer_sheets', []),
        #     question_paper_pdf_bytes=pdfs.get('question_paper')
        # )

        # with ExitStack() as _:
        #     subject = data['testDetails']['subject']
        #     print(f"[Mehul] Subject from manifest: {subject}")
        #     rubricFileName = doRubric(pdfs.get('rubric'), pdfs.get('question_paper'), subject)
        #     # rubricFileName = "something.jsonl"
        #     numThreads = 10
        #     workerFunction = partial(doParallelAnswerSheet, rubricFileName=rubricFileName, subject=subject)
        #     # answerSheetFilesBytes = pdfs.get('answer_sheets', [])
        #     evaluated_results = []
        #     with ThreadPoolExecutor(max_workers=numThreads) as executor:
        #         # Submit all tasks to the thread pool
        #         future_to_answer_sheet = {
        #             executor.submit(workerFunction, answer_sheet): answer_sheet
        #             for answer_sheet in pdfs.get('answer_sheets', [])
        #         }
        #
        #         # Process completed tasks as they finish
        #         from concurrent.futures import as_completed
        #         for future in as_completed(future_to_answer_sheet):
        #             try:
        #                 result = future.result()
        #                 mongo_success = save_to_mongodb(data, result)
        #                 if (mongo_success):
        #                     print("Successfully saved to mongo")
        #                 else:
        #                     print("Failed to save to mongo, but evaluation completed")
        #
        #                 evaluated_results.append(result[0])
        #             except Exception as exc:
        #                 answer_sheet = future_to_answer_sheet[future]
        #                 print(f'Answer sheet {answer_sheet} generated an exception: {exc}')


        with ExitStack() as _:
            subject = data['testDetails']['subject']
            rubricFileName = doRubric(pdfs.get('rubric'), pdfs.get('question_paper'), subject)
            numThreads = 10
            evaluated_results = []
            answerSheetFilesBytesTuple = pdfs.get('answer_sheets', [])

            # Create the initial grading document
            document_id = create_grading_document(data)
            if not document_id:
                print("Failed to create initial grading document")
                return {"statusCode": 500, "body": json.dumps("Failed to create grading document")}

            man = Manager(doParallelAnswerSheet, list(zip(answerSheetFilesBytesTuple, [(rubricFileName, subject) for _ in range(len(answerSheetFilesBytesTuple))])), numThreads)
            results = man.processLoop()

            # Process each result as it comes in and append to the document
            successful_count = 0
            error_count = 0

            for result in results:
                evaluation_text, answer_sheet_key = result

                # Track success/error counts
                if evaluation_text.startswith("Error:"):
                    error_count += 1
                    print(f"⚠️  Error processing answer sheet {answer_sheet_key}: {evaluation_text}")
                else:
                    successful_count += 1
                    print(f"✅ Successfully processed answer sheet {answer_sheet_key}")

                # Append this answer sheet to the existing document
                mongo_success = append_answer_sheet_to_document(document_id, data, result)
                if mongo_success:
                    print(f"Successfully saved answer sheet to document {document_id}")
                else:
                    print(f"Failed to save answer sheet to document {document_id}")

                evaluated_results.append(result[0])  # Keep the original list for return value

            print(f"📊 Processing Summary for document {document_id}:")
            print(f"   ✅ Successful evaluations: {successful_count}")
            print(f"   ⚠️  Failed evaluations: {error_count}")
            print(f"   📄 Total answer sheets: {successful_count + error_count}")

            # Update the document with final statistics
            try:
                client = get_mongo_client()
                is_local = os.environ.get('AWS_SAM_LOCAL')
                db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'
                db = client[db_name]
                collection = db['AegisGrader']

                collection.update_one(
                    {"_id": ObjectId(document_id)},
                    {
                        "$set": {
                            "processingStats": {
                                "totalAnswerSheets": successful_count + error_count,
                                "successfulEvaluations": successful_count,
                                "failedEvaluations": error_count,
                                "completedAt": datetime.now().isoformat()
                            },
                            "updatedAt": datetime.now()
                        }
                    }
                )
                client.close()
                print(f"✅ Updated document {document_id} with final processing statistics")
            except Exception as e:
                print(f"⚠️  Failed to update document with final statistics: {str(e)}")



        with open('/tmp/evaluation_results.json', 'w') as f:
            json.dump(evaluated_results, f)

#         evaluated_results = run_grader_with_pdf_bytes(
#             rubric_pdf_bytes=pdfs.get('rubric'),
#             answer_sheet_pdf_bytes_list=pdfs.get('answer_sheets', []),
#             question_paper_pdf_bytes=pdfs.get('question_paper')
#         )
#         # evaluated_results = "test"
        
#         if evaluated_results:
#             # Save to MongoDB using the manifest data
#             mongo_success = save_to_mongodb(data, evaluated_results)
            
#             if mongo_success:
#                 print("Successfully saved evaluation results to MongoDB")
#             else:
#                 print("Failed to save to MongoDB, but evaluation completed")

        return {"statusCode": 200, "body": json.dumps(evaluated_results)}

    return {"statusCode": 200}
