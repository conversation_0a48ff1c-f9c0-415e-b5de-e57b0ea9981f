from contextlib import ExitStack
import json
from concurrent.futures import Thread<PERSON>oolExecutor
from functools import partial
import boto3
import sys
import os
from datetime import datetime
import pymongo
from bson import ObjectId

# For local testing, add layer path
if os.environ.get('AWS_SAM_LOCAL'):
    sys.path.append('/opt/python')
    sys.path.append('/opt/python/python')
else:
    sys.path.append('/opt/python')
    sys.path.append('/opt/python/python')

from aegisGrader import run_grader_with_pdf_bytes, doRubric, doParallelAnswerSheet
from parallels import Manager

# Use default credential chain (AWS CLI for local, IAM role for Lambda)
s3_client = boto3.client('s3')
                        

# MongoDB setup
def get_mongo_client():
    # Use test DB if running locally, prod DB otherwise
    is_local = os.environ.get('AWS_SAM_LOCAL')
    env_var = 'MONGO_URI_TEST' if is_local else 'MONGO_URI_PROD'
    mongo_uri = os.environ.get(env_var)
    
    print(f"Environment: {'LOCAL' if is_local else 'PROD'}")
    print(f"Using environment variable: {env_var}")
    print(f"Connection string exists: {bool(mongo_uri)}")
    
    if not mongo_uri:
        raise ValueError(f"{env_var} environment variable not set")
    
    # Log connection string (masked for security)
    masked_uri = mongo_uri.replace(mongo_uri.split('@')[0].split('//')[1], '***:***') if '@' in mongo_uri else mongo_uri
    print(f"Connecting to: {masked_uri}")
    
    try:
        client = pymongo.MongoClient(mongo_uri, serverSelectionTimeoutMS=5000)
        # Test the connection
        client.admin.command('ping')
        print("✅ MongoDB connection successful")
        return client
    except Exception as e:
        print(f"❌ MongoDB connection failed: {str(e)}")
        raise

def save_to_mongodb(manifest_data, evaluation_result):
    """Save graded results to MongoDB"""
    try:
        if (not evaluation_result):
            return

        print("Attempting to connect to MongoDB...")
        client = get_mongo_client()
        
        is_local = os.environ.get('AWS_SAM_LOCAL')
        db_name = 'AegisScholarTestDb' if is_local else 'AegisScholarDb'
        print(f"Using database: {db_name}")
        
        db = client[db_name]
        collection = db['AegisGrader']
        
        print("Connected to collection successfully")
        
        # Extract files by purpose
        answer_sheet = None
        question_paper = None
        rubric = None
        
        for file in manifest_data.get('files', []):
            if file.get('filePurpose') == 'answer_sheet' and file.get('key') == evaluation_result[1]:
                # answer_sheets.append(file)
                answer_sheet = file
            elif file.get('filePurpose') == 'question_paper':
                question_paper = file
            elif file.get('filePurpose') == 'rubric':
                rubric = file
        
        # Parse evaluation results if they're in list format
        # if isinstance(evaluation_results, list):
        #     evaluations = evaluation_results
        # else:
        #     # If it's a string, try to split or use as single evaluation
        #     evaluations = [evaluation_results] if evaluation_results else []
        
        # Create a document for each answer sheet
            # for i, answer_sheet in enumerate(answer_sheets):
        #evaluation_result = evaluations[i] if i < len(evaluations) else ""
        
        aegis_grader_doc = {
            "testDetails": {
                "createdBy": manifest_data['testDetails'].get('CreatedBy', ''),
                "className": manifest_data['testDetails'].get('className', ''),
                "subject": manifest_data['testDetails'].get('subject', ''),
                "date": manifest_data['testDetails'].get('date', datetime.now().isoformat())
            },
            "answerSheets": [{
                "id": answer_sheet.get('key'),
                "studentName": answer_sheet.get('studentName', ''),
                "rollNumber": answer_sheet.get('rollNumber', ''),
                "pdfUrl": answer_sheet.get('key'),  # Using S3 key as URL reference
                "timestamp": answer_sheet.get('timestamp'),
                "className": manifest_data.get('className', ''),
                "evaluationResult": evaluation_result
            }],
            "questionPaper": {
                "type": "questionPaper",
                "pdfUrl": question_paper.get('key', '') if question_paper else '',
                "timestamp": question_paper.get('timestamp') if question_paper else int(datetime.now().timestamp() * 1000)
            },
            "rubric": {
                "type": "rubric", 
                "pdfUrl": rubric.get('key', '') if rubric else '',
                "timestamp": rubric.get('timestamp') if rubric else int(datetime.now().timestamp() * 1000)
            },
            "createdAt": datetime.now(),
            "updatedAt": datetime.now()
        }
        
        result = collection.insert_one(aegis_grader_doc)
        print(f"Saved evaluation for student {answer_sheet.get('studentName', 'Unknown')} with ID: {result.inserted_id}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"Error saving to MongoDB: {str(e)}")
        return False

def process_files(data):
    answer_sheet_keys = []
    question_paper_key = None
    rubric_key = None

    for file in data['files']:
        purpose = file.get('filePurpose')
        key = file.get('key')
        if purpose == 'answer_sheet':
            answer_sheet_keys.append(key)
        elif purpose == 'question_paper':
            question_paper_key = key
        elif purpose == 'rubric':
            rubric_key = key

    return {
        "answer_sheet_keys": answer_sheet_keys,
        "question_paper_key": question_paper_key,
        "rubric_key": rubric_key
    }

def download_pdf(bucket, key):
    response = s3_client.get_object(Bucket=bucket, Key=key)
    pdf_bytes = response['Body'].read()
    return pdf_bytes

def test_mongodb_with_dummy_data():
    """Test MongoDB connection with dummy data"""
    dummy_manifest = {
        "className": "Test Class 12A",
        "subject": "Mathematics",
        "date": "2024-01-15",
        "files": [
            {
                "fileName": "test_answer.pdf",
                "key": "test_answer_key_123",
                "filePurpose": "answer_sheet",
                "studentName": "John Doe",
                "rollNumber": "12345",
                "timestamp": 1642204800000
            },
            {
                "fileName": "test_question.pdf",
                "key": "test_question_key_123",
                "filePurpose": "question_paper",
                "timestamp": 1642204800000
            },
            {
                "fileName": "test_rubric.pdf",
                "key": "test_rubric_key_123",
                "filePurpose": "rubric",
                "timestamp": 1642204800000
            }
        ]
    }
    
    dummy_evaluation = """
    <evaluation>
    <total_marks>85</total_marks>
    <maximum_possible_marks>100</maximum_possible_marks>
    <percentage_score>85</percentage_score>
    
    <question number="1">
    <marks_awarded>20</marks_awarded>
    <marks_possible>25</marks_possible>
    <feedback>Good understanding of calculus concepts. Minor error in final calculation.</feedback>
    </question>
    </evaluation>
    """
    
    print("Testing MongoDB save with dummy data...")
    success = save_to_mongodb(dummy_manifest, dummy_evaluation)
    
    if success:
        print("✅ MongoDB test successful!")
    else:
        print("❌ MongoDB test failed!")
    
    return success

def lambda_handler(event, context):
    records = event["Records"]
    # print("Event => ", event)

    for record in records:
        body = json.loads(record["body"])
        s3 = body["Records"][0]["s3"]
        bucket = s3["bucket"]["name"]
        key = s3["object"]["key"]

        # Fetch and parse the manifest JSON from S3
        print(f"[Mehul] Debug got key: {key} in bucket: {bucket}")
        response = s3_client.get_object(Bucket=bucket, Key=key)
        # print("Response => ", response)
        content = response['Body'].read().decode('utf-8')
        data = json.loads(content)

        # Get the keys for the PDFs
        result = process_files(data)
        answer_sheet_keys = result["answer_sheet_keys"]
        question_paper_key = result["question_paper_key"]
        rubric_key = result["rubric_key"]

        # Download PDFs and store in variable (ram memory)
        pdfs = {}

        if question_paper_key:
            pdfs['question_paper'] = download_pdf(bucket, question_paper_key)
        if rubric_key:
            pdfs['rubric'] = download_pdf(bucket, rubric_key)
        pdfs['answer_sheets'] = []
        # answerSheetBytesTupleList = []
        for ans_key in answer_sheet_keys:
            pdfs['answer_sheets'].append((download_pdf(bucket, ans_key), ans_key))


        # evaluated_results = run_grader_with_pdf_bytes(
        #     rubric_pdf_bytes=pdfs.get('rubric'),
        #     answer_sheet_pdf_bytes_list=pdfs.get('answer_sheets', []),
        #     question_paper_pdf_bytes=pdfs.get('question_paper')
        # )

        # with ExitStack() as _:
        #     subject = data['testDetails']['subject']
        #     print(f"[Mehul] Subject from manifest: {subject}")
        #     rubricFileName = doRubric(pdfs.get('rubric'), pdfs.get('question_paper'), subject)
        #     # rubricFileName = "something.jsonl"
        #     numThreads = 10
        #     workerFunction = partial(doParallelAnswerSheet, rubricFileName=rubricFileName, subject=subject)
        #     # answerSheetFilesBytes = pdfs.get('answer_sheets', [])
        #     evaluated_results = []
        #     with ThreadPoolExecutor(max_workers=numThreads) as executor:
        #         # Submit all tasks to the thread pool
        #         future_to_answer_sheet = {
        #             executor.submit(workerFunction, answer_sheet): answer_sheet
        #             for answer_sheet in pdfs.get('answer_sheets', [])
        #         }
        #
        #         # Process completed tasks as they finish
        #         from concurrent.futures import as_completed
        #         for future in as_completed(future_to_answer_sheet):
        #             try:
        #                 result = future.result()
        #                 mongo_success = save_to_mongodb(data, result)
        #                 if (mongo_success):
        #                     print("Successfully saved to mongo")
        #                 else:
        #                     print("Failed to save to mongo, but evaluation completed")
        #
        #                 evaluated_results.append(result[0])
        #             except Exception as exc:
        #                 answer_sheet = future_to_answer_sheet[future]
        #                 print(f'Answer sheet {answer_sheet} generated an exception: {exc}')


        with ExitStack() as _:
            subject = data['testDetails']['subject']
            rubricFileName = doRubric(pdfs.get('rubric'), pdfs.get('question_paper'), subject)
            numThreads = 10
            evaluated_results = []
            i = 0
            answerSheetFilesBytesTuple = pdfs.get('answer_sheets', [])
            man = Manager(doParallelAnswerSheet, list(zip(answerSheetFilesBytesTuple, [(rubricFileName, subject) for _ in range(len(answerSheetFilesBytesTuple))])), numThreads)
            results = man.processLoop()
            for i in results:
                _ = save_to_mongodb(data, i)
                evaluated_results.append(i[0])



        with open('/tmp/evaluation_results.json', 'w') as f:
            json.dump(evaluated_results, f)

#         evaluated_results = run_grader_with_pdf_bytes(
#             rubric_pdf_bytes=pdfs.get('rubric'),
#             answer_sheet_pdf_bytes_list=pdfs.get('answer_sheets', []),
#             question_paper_pdf_bytes=pdfs.get('question_paper')
#         )
#         # evaluated_results = "test"
        
#         if evaluated_results:
#             # Save to MongoDB using the manifest data
#             mongo_success = save_to_mongodb(data, evaluated_results)
            
#             if mongo_success:
#                 print("Successfully saved evaluation results to MongoDB")
#             else:
#                 print("Failed to save to MongoDB, but evaluation completed")

        return {"statusCode": 200, "body": json.dumps(evaluated_results)}

    return {"statusCode": 200}
